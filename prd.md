# **产品需求文档（PRD）**

**项目名称**：面试宝典（Interview Master）
**版本**：v1.0
**文档状态**：初稿
**作者**：左浩

---

## 1. 产品概述

**1.1 产品背景**
企业在面试不同岗位候选人时，需要大量高质量的题目与解析，并且题目需要根据岗位和候选人特点进行筛选。现有题库分散且更新不便，缺少统一管理与快速调取的工具。
“面试宝典”旨在为面试官提供一个**岗位维度化的面试题库管理与调用平台**，支持随机抽题、题目解析、面试记录与评分，并且可以根据候选人简历自动筛选题目。

**1.2 产品目标**

* 搭建结构化的岗位面试题库，支持按岗位→知识维度→题目管理
* 支持面试时随机出题，并展示解析
* 支持候选人答案记录与面试官打分
* 支持上传简历，根据简历自动推荐/筛选题目
* 支持题库持续维护与扩展

---

## 2. 功能需求

### 2.1 功能结构图

```
面试宝典
 ├── 题库管理
 │    ├── 岗位管理（如前端、后端、测试）
 │    ├── 知识维度管理（如 HTTP、CSS、JS 基础）
 │    └── 题目管理（题干、解析、标签、难度）
 │
 ├── 面试管理
 │    ├── 岗位选择
 │    ├── 随机抽题
 │    ├── 展示题目 & 解析
 │    ├── 答案输入框
 │    ├── 面试官打分（1-5 分）
 │    └── 面试记录保存
 │
 ├── 简历解析 & 题目推荐
 │    ├── 简历上传（PDF/DOCX）
 │    ├── 解析（技能关键词提取）
 │    ├── 匹配知识维度
 │    └── 推荐题目
 │
 ├── 系统设置
 │    ├── 用户管理
 │    ├── 权限管理
 │    └── 数据导入导出
```

---

### 2.2 详细功能描述

#### 2.2.1 题库管理

| 功能     | 描述                          | 备注         |
| ------ | --------------------------- | ---------- |
| 岗位管理   | 创建/编辑/删除岗位（如前端开发）           | 岗位作为题目一级分类 |
| 知识维度管理 | 创建/编辑/删除知识维度（如 HTTP、CSS、JS） | 隶属于岗位      |
| 题目管理   | 添加/编辑/删除题目，包含题干、解析、难度、标签    | 标签用于快速检索   |

#### 2.2.2 面试管理

| 功能        | 描述                | 备注       |
| --------- | ----------------- | -------- |
| 岗位选择      | 面试官选择面试岗位         | 作为抽题范围   |
| 随机抽题      | 系统根据岗位随机抽题        | 支持指定数量   |
| 展示题目 & 解析 | 默认先展示题目，解析可点击查看   | 防止提前泄露答案 |
| 答案输入      | 面试官在题目下方输入候选人回答要点 | 保存为面试记录  |
| 打分功能      | 面试官对每道题打 1-5 分    | 保存到记录    |
| 面试记录保存    | 保存面试题、答案、分数、候选人信息 | 用于后续评估   |

#### 2.2.3 简历解析 & 题目推荐

| 功能   | 描述                                       | 备注                       |
| ---- | ---------------------------------------- | ------------------------ |
| 简历上传 | 上传 PDF/DOCX 格式简历                         | 限制大小 5MB                 |
| 解析简历 | 使用 Node.js 服务调用 NLP（如 OpenAI API）提取技能关键词 | 关键词如 Vue、React、Node、HTTP |
| 匹配维度 | 根据关键词与题库维度的映射规则，筛选相关题目                   | 例如简历含 Vue → 推荐 Vue 相关题   |
| 推荐题目 | 展示推荐题列表，支持随机抽题                           | 可与普通抽题结合                 |

---

## 3. 页面与交互

### 3.1 页面列表

1. **首页（岗位选择）**
2. **题库管理页面**

   * 岗位管理
   * 知识维度管理
   * 题目管理
3. **面试进行页面**

   * 当前题目展示
   * 输入框 & 打分按钮
   * 下一题按钮
4. **面试记录页面**

   * 面试者姓名、岗位、题目、答案、分数
5. **简历解析页面**

   * 上传按钮
   * 技能关键词展示
   * 推荐题目列表

---

### 3.2 样例交互流程（面试模式）

1. 面试官登录 → 选择岗位（前端）
2. 上传候选人简历（可选） → 系统解析技能关键词
3. 根据岗位 + 简历技能推荐题目 → 系统随机出 10 道
4. 面试官逐题提问 → 候选人口述回答 → 面试官输入要点并打分
5. 面试结束 → 保存记录 → 导出为 PDF/Excel

---

## 4. 数据结构（简化版）

```ts
// 岗位
interface Position {
  id: string;
  name: string; // 前端开发
}

// 知识维度
interface Dimension {
  id: string;
  positionId: string;
  name: string; // HTTP
}

// 题目
interface Question {
  id: string;
  dimensionId: string;
  title: string;
  answer: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
}

// 面试记录
interface InterviewRecord {
  id: string;
  candidateName: string;
  positionId: string;
  questions: {
    questionId: string;
    candidateAnswer: string;
    score: number; // 1-5
  }[];
  totalScore: number;
}
```

---

## 5. 技术方案

### 5.1 前端

* **框架**：Vue3 + Vite + TypeScript
* **UI 库**：shadcn/ui（Tailwind CSS 风格），整体设计参考苹果风格（圆角、阴影、简洁）
* **功能点**：

  * 富文本编辑器（题目编辑）
  * 文件上传组件（简历）
  * 打分组件（1-5 星）
  * 分步面试流程组件

### 5.2 后端

* **技术栈**：Node.js + Express / Koa
* **数据库**：PostgreSQL 或 MySQL（结构化存储题库、记录）
* **简历解析**：调用 OpenAI API 或其他 NLP 库（如 textract + keyword-extractor）
* **接口示例**：

  * GET `/positions` → 获取岗位列表
  * GET `/questions?positionId=&dimensionId=` → 获取题目
  * POST `/interviews` → 保存面试记录
  * POST `/resume/parse` → 上传并解析简历

### 5.3 部署

* 前后端分离，Nginx 反向代理
* 数据库与静态文件存储分离（题库、简历）
* 支持 Docker 部署

---
