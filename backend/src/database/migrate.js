const { pool } = require('./config');

const createTables = async () => {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting database migration...');

    // 启用UUID扩展
    await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');

    // 创建岗位表
    await client.query(`
      CREATE TABLE IF NOT EXISTS positions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建知识维度表
    await client.query(`
      CREATE TABLE IF NOT EXISTS dimensions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        position_id UUID NOT NULL REFERENCES positions(id) ON DELETE CASCADE,
        name VARCHA<PERSON>(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(position_id, name)
      )
    `);

    // 创建题目表
    await client.query(`
      CREATE TABLE IF NOT EXISTS questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        dimension_id UUID NOT NULL REFERENCES dimensions(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        answer TEXT NOT NULL,
        difficulty VARCHAR(10) CHECK (difficulty IN ('easy', 'medium', 'hard')) DEFAULT 'medium',
        tags TEXT[] DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建面试记录表
    await client.query(`
      CREATE TABLE IF NOT EXISTS interview_records (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        candidate_name VARCHAR(100) NOT NULL,
        position_id UUID NOT NULL REFERENCES positions(id),
        interview_date DATE NOT NULL,
        total_score DECIMAL(3,1) DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建面试题目记录表
    await client.query(`
      CREATE TABLE IF NOT EXISTS interview_questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        interview_record_id UUID NOT NULL REFERENCES interview_records(id) ON DELETE CASCADE,
        question_id UUID NOT NULL REFERENCES questions(id),
        candidate_answer TEXT,
        score DECIMAL(2,1) CHECK (score >= 1 AND score <= 5),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建简历解析记录表
    await client.query(`
      CREATE TABLE IF NOT EXISTS resume_analyses (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        skills TEXT[] DEFAULT '{}',
        raw_content TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建用户表（可选，用于权限管理）
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'interviewer' CHECK (role IN ('admin', 'interviewer')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建索引以提高查询性能
    await client.query('CREATE INDEX IF NOT EXISTS idx_dimensions_position_id ON dimensions(position_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_questions_dimension_id ON questions(dimension_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_interview_records_position_id ON interview_records(position_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_interview_records_date ON interview_records(interview_date)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_interview_questions_record_id ON interview_questions(interview_record_id)');

    // 创建更新时间触发器函数
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // 为需要的表创建更新时间触发器
    const tablesWithUpdatedAt = ['positions', 'dimensions', 'questions', 'interview_records', 'users'];
    
    for (const table of tablesWithUpdatedAt) {
      await client.query(`
        DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};
        CREATE TRIGGER update_${table}_updated_at
          BEFORE UPDATE ON ${table}
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column()
      `);
    }

    console.log('✅ Database migration completed successfully!');
    console.log('📊 Created tables: positions, dimensions, questions, interview_records, interview_questions, resume_analyses, users');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
};

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  createTables()
    .then(() => {
      console.log('🎉 Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { createTables };
