const { createTables } = require('./migrate');
const { seedData } = require('./seed');

const initializeDatabase = async () => {
  try {
    console.log('🚀 Initializing Interview Master Database...');
    console.log('=====================================');
    
    // 1. 创建表结构
    await createTables();
    
    // 2. 插入种子数据
    await seedData();
    
    console.log('=====================================');
    console.log('🎉 Database initialization completed successfully!');
    console.log('📊 Your Interview Master database is ready to use.');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start the backend server: npm run dev');
    console.log('2. Start the frontend server: cd ../frontend && npm run dev');
    console.log('3. Visit http://localhost:5173 to use the application');
    
  } catch (error) {
    console.error('💥 Database initialization failed:', error);
    console.log('');
    console.log('Troubleshooting:');
    console.log('1. Make sure PostgreSQL is running');
    console.log('2. Check your database connection settings in .env file');
    console.log('3. Ensure the database exists and user has proper permissions');
    throw error;
  }
};

// 如果直接运行此文件，执行完整初始化
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      process.exit(1);
    });
}

module.exports = { initializeDatabase };
