const { pool } = require('./config');

const seedData = async () => {
  const client = await pool.connect();
  
  try {
    console.log('🌱 Starting database seeding...');

    // 检查是否已有数据
    const positionsCount = await client.query('SELECT COUNT(*) FROM positions');
    if (parseInt(positionsCount.rows[0].count) > 0) {
      console.log('📊 Database already has data, skipping seed...');
      return;
    }

    // 插入岗位数据
    const positions = [
      { name: '前端开发', description: '负责Web前端开发，包括Vue、React等框架' },
      { name: '后端开发', description: '负责服务端开发，包括API设计、数据库设计等' },
      { name: '全栈开发', description: '同时负责前端和后端开发工作' },
      { name: '测试工程师', description: '负责软件测试，包括功能测试、自动化测试等' },
      { name: '产品经理', description: '负责产品规划、需求分析和项目管理' }
    ];

    const insertedPositions = [];
    for (const position of positions) {
      const result = await client.query(
        'INSERT INTO positions (name, description) VALUES ($1, $2) RETURNING *',
        [position.name, position.description]
      );
      insertedPositions.push(result.rows[0]);
    }

    // 为前端开发岗位插入知识维度
    const frontendPosition = insertedPositions.find(p => p.name === '前端开发');
    const frontendDimensions = [
      { name: 'JavaScript基础', description: '包括ES6+、闭包、原型链等' },
      { name: 'Vue框架', description: 'Vue2/3响应式原理、组件通信等' },
      { name: 'React框架', description: 'React Hooks、状态管理等' },
      { name: 'CSS样式', description: 'CSS3、预处理器、响应式设计等' },
      { name: 'HTTP协议', description: '网络请求、缓存、安全等' },
      { name: '工程化工具', description: 'Webpack、Vite、构建优化等' }
    ];

    const insertedDimensions = [];
    for (const dimension of frontendDimensions) {
      const result = await client.query(
        'INSERT INTO dimensions (position_id, name, description) VALUES ($1, $2, $3) RETURNING *',
        [frontendPosition.id, dimension.name, dimension.description]
      );
      insertedDimensions.push(result.rows[0]);
    }

    // 为JavaScript基础维度插入题目
    const jsDimension = insertedDimensions.find(d => d.name === 'JavaScript基础');
    const jsQuestions = [
      {
        title: '什么是闭包？你在项目中用过闭包的场景吗？',
        answer: '闭包是指函数可以访问其外部函数作用域中的变量，即使外部函数已经执行结束。常见应用场景包括：模块化封装、事件处理、防抖节流等。',
        difficulty: 'medium',
        tags: ['闭包', '作用域', '内存管理']
      },
      {
        title: 'var、let、const的区别？什么时候用哪种？',
        answer: 'var：函数作用域、变量提升、可重复声明；let：块级作用域、不可重复声明、暂时性死区；const：块级作用域、必须初始化、不能重新赋值。推荐默认使用const，需要修改时用let，避免使用var。',
        difficulty: 'easy',
        tags: ['变量声明', '作用域', 'ES6']
      },
      {
        title: 'Promise和async/await的区别？',
        answer: 'Promise使用链式调用.then/.catch，代码可读性差时嵌套深；async/await是基于Promise的语法糖，像同步写法，更易维护。错误处理：Promise用.catch，async/await用try/catch。',
        difficulty: 'medium',
        tags: ['异步编程', 'Promise', 'async/await']
      }
    ];

    for (const question of jsQuestions) {
      await client.query(
        'INSERT INTO questions (dimension_id, title, answer, difficulty, tags) VALUES ($1, $2, $3, $4, $5)',
        [jsDimension.id, question.title, question.answer, question.difficulty, question.tags]
      );
    }

    // 为Vue框架维度插入题目
    const vueDimension = insertedDimensions.find(d => d.name === 'Vue框架');
    const vueQuestions = [
      {
        title: 'Vue响应式原理？Vue2和Vue3在实现上有何不同？',
        answer: 'Vue2基于Object.defineProperty，只能监听已存在的属性；Vue3基于Proxy，可监听对象和数组所有操作，性能更好，支持弱引用防止内存泄漏。',
        difficulty: 'hard',
        tags: ['响应式', 'Proxy', 'Object.defineProperty']
      },
      {
        title: '父子组件通信有哪些方式？',
        answer: '主要方式：props/emit、provide/inject、Vuex/Pinia全局状态、EventBus。还可以通过URL参数、localStorage等非直接通信方式。',
        difficulty: 'medium',
        tags: ['组件通信', 'props', 'emit', '状态管理']
      }
    ];

    for (const question of vueQuestions) {
      await client.query(
        'INSERT INTO questions (dimension_id, title, answer, difficulty, tags) VALUES ($1, $2, $3, $4, $5)',
        [vueDimension.id, question.title, question.answer, question.difficulty, question.tags]
      );
    }

    // 为后端开发岗位插入一些基础数据
    const backendPosition = insertedPositions.find(p => p.name === '后端开发');
    const backendDimensions = [
      { name: 'Node.js', description: 'Node.js运行时、Express框架等' },
      { name: '数据库', description: 'SQL、NoSQL、数据库设计等' },
      { name: 'API设计', description: 'RESTful API、GraphQL等' }
    ];

    for (const dimension of backendDimensions) {
      await client.query(
        'INSERT INTO dimensions (position_id, name, description) VALUES ($1, $2, $3)',
        [backendPosition.id, dimension.name, dimension.description]
      );
    }

    console.log('✅ Database seeding completed successfully!');
    console.log('📊 Inserted:');
    console.log(`   - ${positions.length} positions`);
    console.log(`   - ${frontendDimensions.length + backendDimensions.length} dimensions`);
    console.log(`   - ${jsQuestions.length + vueQuestions.length} questions`);
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  } finally {
    client.release();
  }
};

// 如果直接运行此文件，执行种子数据插入
if (require.main === module) {
  seedData()
    .then(() => {
      console.log('🎉 Seed script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seed script failed:', error);
      process.exit(1);
    });
}

module.exports = { seedData };
