const express = require('express');
const router = express.Router();
const Question = require('../models/Question');
const Joi = require('joi');

// 验证模式
const questionSchema = Joi.object({
  dimensionId: Joi.string().uuid().required(),
  title: Joi.string().min(10).max(1000).required(),
  answer: Joi.string().min(10).max(5000).required(),
  difficulty: Joi.string().valid('easy', 'medium', 'hard').default('medium'),
  tags: Joi.array().items(Joi.string().max(50)).default([])
});

const querySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  positionId: Joi.string().uuid(),
  dimensionId: Joi.string().uuid(),
  difficulty: Joi.string().valid('easy', 'medium', 'hard'),
  search: Joi.string().max(100)
});

// 获取题目列表（支持分页、筛选）
router.get('/', async (req, res) => {
  try {
    const { error, value } = querySchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        details: error.details.map(d => d.message)
      });
    }

    const result = await Question.findAll(value);
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error fetching questions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch questions'
    });
  }
});

// 随机获取题目
router.get('/random', async (req, res) => {
  try {
    const { positionId, dimensionIds, difficulty, count = 10 } = req.query;

    const options = {
      positionId,
      dimensionIds: dimensionIds ? dimensionIds.split(',') : [],
      difficulty,
      count: parseInt(count)
    };

    const questions = await Question.findRandom(options);
    res.json({
      success: true,
      data: questions
    });
  } catch (error) {
    console.error('Error fetching random questions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch random questions'
    });
  }
});

// 根据技能推荐题目
router.get('/recommend', async (req, res) => {
  try {
    const { skills, limit = 20 } = req.query;

    if (!skills) {
      return res.status(400).json({
        success: false,
        error: 'Skills parameter is required'
      });
    }

    const skillsArray = Array.isArray(skills) ? skills : skills.split(',');
    const questions = await Question.findBySkills(skillsArray, parseInt(limit));

    res.json({
      success: true,
      data: questions
    });
  } catch (error) {
    console.error('Error recommending questions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to recommend questions'
    });
  }
});

// 创建新题目
router.post('/', async (req, res) => {
  try {
    const { error, value } = questionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    const question = await Question.create(value);
    res.status(201).json({
      success: true,
      data: question,
      message: 'Question created successfully'
    });
  } catch (error) {
    console.error('Error creating question:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create question'
    });
  }
});

// 获取单个题目
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const question = await Question.findById(id);

    if (!question) {
      return res.status(404).json({
        success: false,
        error: 'Question not found'
      });
    }

    res.json({
      success: true,
      data: question
    });
  } catch (error) {
    console.error('Error fetching question:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch question'
    });
  }
});

// 更新题目
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { error, value } = questionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    const question = await Question.update(id, value);
    if (!question) {
      return res.status(404).json({
        success: false,
        error: 'Question not found'
      });
    }

    res.json({
      success: true,
      data: question,
      message: 'Question updated successfully'
    });
  } catch (error) {
    console.error('Error updating question:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update question'
    });
  }
});

// 删除题目
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const question = await Question.delete(id);

    if (!question) {
      return res.status(404).json({
        success: false,
        error: 'Question not found'
      });
    }

    res.json({
      success: true,
      data: question,
      message: 'Question deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting question:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete question'
    });
  }
});

module.exports = router;
