const express = require('express');
const router = express.Router();
const Position = require('../models/Position');
const Joi = require('joi');

// 验证模式
const positionSchema = Joi.object({
  name: Joi.string().min(1).max(100).required(),
  description: Joi.string().max(500).allow('', null)
});

// 获取所有岗位
router.get('/', async (req, res) => {
  try {
    const positions = await Position.findAll();
    res.json({
      success: true,
      data: positions
    });
  } catch (error) {
    console.error('Error fetching positions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch positions'
    });
  }
});

// 获取岗位统计信息
router.get('/stats', async (req, res) => {
  try {
    const stats = await Position.getStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching position stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch position statistics'
    });
  }
});

// 创建新岗位
router.post('/', async (req, res) => {
  try {
    // 验证输入数据
    const { error, value } = positionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    const { name, description } = value;

    // 检查名称是否已存在
    const exists = await Position.existsByName(name);
    if (exists) {
      return res.status(409).json({
        success: false,
        error: 'Position name already exists'
      });
    }

    const position = await Position.create({ name, description });

    res.status(201).json({
      success: true,
      data: position,
      message: 'Position created successfully'
    });
  } catch (error) {
    console.error('Error creating position:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create position'
    });
  }
});

// 获取单个岗位
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { include } = req.query;

    let position;
    if (include === 'dimensions') {
      position = await Position.findByIdWithDimensions(id);
    } else {
      position = await Position.findById(id);
    }

    if (!position) {
      return res.status(404).json({
        success: false,
        error: 'Position not found'
      });
    }

    res.json({
      success: true,
      data: position
    });
  } catch (error) {
    console.error('Error fetching position:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch position'
    });
  }
});

// 更新岗位
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 验证输入数据
    const { error, value } = positionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }

    const { name, description } = value;

    // 检查岗位是否存在
    const existingPosition = await Position.findById(id);
    if (!existingPosition) {
      return res.status(404).json({
        success: false,
        error: 'Position not found'
      });
    }

    // 检查名称是否与其他岗位冲突
    const nameExists = await Position.existsByName(name, id);
    if (nameExists) {
      return res.status(409).json({
        success: false,
        error: 'Position name already exists'
      });
    }

    const position = await Position.update(id, { name, description });

    res.json({
      success: true,
      data: position,
      message: 'Position updated successfully'
    });
  } catch (error) {
    console.error('Error updating position:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update position'
    });
  }
});

// 删除岗位
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查岗位是否存在
    const existingPosition = await Position.findById(id);
    if (!existingPosition) {
      return res.status(404).json({
        success: false,
        error: 'Position not found'
      });
    }

    const deletedPosition = await Position.delete(id);

    res.json({
      success: true,
      data: deletedPosition,
      message: 'Position deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting position:', error);

    // 处理外键约束错误
    if (error.code === '23503') {
      return res.status(409).json({
        success: false,
        error: 'Cannot delete position with existing dimensions or interview records'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete position'
    });
  }
});

module.exports = router;
