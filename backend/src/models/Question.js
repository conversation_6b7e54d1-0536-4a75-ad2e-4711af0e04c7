const db = require('../database/config');

class Question {
  // 获取题目列表（支持分页和筛选）
  static async findAll(options = {}) {
    const { 
      page = 1, 
      limit = 20, 
      positionId, 
      dimensionId, 
      difficulty, 
      search 
    } = options;
    
    const offset = (page - 1) * limit;
    let whereConditions = [];
    let params = [];
    let paramIndex = 1;

    if (positionId) {
      whereConditions.push(`d.position_id = $${paramIndex++}`);
      params.push(positionId);
    }

    if (dimensionId) {
      whereConditions.push(`q.dimension_id = $${paramIndex++}`);
      params.push(dimensionId);
    }

    if (difficulty) {
      whereConditions.push(`q.difficulty = $${paramIndex++}`);
      params.push(difficulty);
    }

    if (search) {
      whereConditions.push(`(q.title ILIKE $${paramIndex++} OR q.answer ILIKE $${paramIndex++})`);
      params.push(`%${search}%`, `%${search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取总数
    const countQuery = `
      SELECT COUNT(*)
      FROM questions q
      JOIN dimensions d ON q.dimension_id = d.id
      JOIN positions p ON d.position_id = p.id
      ${whereClause}
    `;
    const countResult = await db.query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // 获取数据
    const dataQuery = `
      SELECT q.*, d.name as dimension_name, p.name as position_name
      FROM questions q
      JOIN dimensions d ON q.dimension_id = d.id
      JOIN positions p ON d.position_id = p.id
      ${whereClause}
      ORDER BY q.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;
    params.push(limit, offset);
    
    const dataResult = await db.query(dataQuery, params);

    return {
      data: dataResult.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 根据ID获取题目
  static async findById(id) {
    const result = await db.query(`
      SELECT q.*, d.name as dimension_name, p.name as position_name
      FROM questions q
      JOIN dimensions d ON q.dimension_id = d.id
      JOIN positions p ON d.position_id = p.id
      WHERE q.id = $1
    `, [id]);
    return result.rows[0];
  }

  // 随机获取题目
  static async findRandom(options = {}) {
    const { positionId, dimensionIds = [], difficulty, count = 10 } = options;
    
    let whereConditions = [];
    let params = [];
    let paramIndex = 1;

    if (positionId) {
      whereConditions.push(`d.position_id = $${paramIndex++}`);
      params.push(positionId);
    }

    if (dimensionIds.length > 0) {
      whereConditions.push(`q.dimension_id = ANY($${paramIndex++})`);
      params.push(dimensionIds);
    }

    if (difficulty) {
      whereConditions.push(`q.difficulty = $${paramIndex++}`);
      params.push(difficulty);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const query = `
      SELECT q.*, d.name as dimension_name, p.name as position_name
      FROM questions q
      JOIN dimensions d ON q.dimension_id = d.id
      JOIN positions p ON d.position_id = p.id
      ${whereClause}
      ORDER BY RANDOM()
      LIMIT $${paramIndex++}
    `;
    params.push(count);

    const result = await db.query(query, params);
    return result.rows;
  }

  // 创建新题目
  static async create(data) {
    const { dimensionId, title, answer, difficulty = 'medium', tags = [] } = data;
    const result = await db.query(
      'INSERT INTO questions (dimension_id, title, answer, difficulty, tags) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [dimensionId, title, answer, difficulty, tags]
    );
    return result.rows[0];
  }

  // 更新题目
  static async update(id, data) {
    const { dimensionId, title, answer, difficulty, tags } = data;
    const result = await db.query(
      'UPDATE questions SET dimension_id = $1, title = $2, answer = $3, difficulty = $4, tags = $5 WHERE id = $6 RETURNING *',
      [dimensionId, title, answer, difficulty, tags, id]
    );
    return result.rows[0];
  }

  // 删除题目
  static async delete(id) {
    const result = await db.query('DELETE FROM questions WHERE id = $1 RETURNING *', [id]);
    return result.rows[0];
  }

  // 根据技能关键词推荐题目
  static async findBySkills(skills, limit = 20) {
    const result = await db.query(`
      SELECT q.*, d.name as dimension_name, p.name as position_name,
             ts_rank(to_tsvector('english', q.title || ' ' || q.answer || ' ' || array_to_string(q.tags, ' ')), 
                     plainto_tsquery('english', $1)) as rank
      FROM questions q
      JOIN dimensions d ON q.dimension_id = d.id
      JOIN positions p ON d.position_id = p.id
      WHERE to_tsvector('english', q.title || ' ' || q.answer || ' ' || array_to_string(q.tags, ' ')) 
            @@ plainto_tsquery('english', $1)
      ORDER BY rank DESC
      LIMIT $2
    `, [skills.join(' '), limit]);
    
    return result.rows;
  }
}

module.exports = Question;
