const db = require('../database/config');

class Position {
  // 获取所有岗位
  static async findAll() {
    const result = await db.query(`
      SELECT p.*, 
             COUNT(d.id) as dimension_count,
             COUNT(q.id) as question_count
      FROM positions p
      LEFT JOIN dimensions d ON p.id = d.position_id
      LEFT JOIN questions q ON d.id = q.dimension_id
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `);
    return result.rows;
  }

  // 根据ID获取岗位
  static async findById(id) {
    const result = await db.query('SELECT * FROM positions WHERE id = $1', [id]);
    return result.rows[0];
  }

  // 获取岗位及其维度
  static async findByIdWithDimensions(id) {
    const positionResult = await db.query('SELECT * FROM positions WHERE id = $1', [id]);
    if (positionResult.rows.length === 0) return null;

    const dimensionsResult = await db.query(`
      SELECT d.*, COUNT(q.id) as question_count
      FROM dimensions d
      LEFT JOIN questions q ON d.id = q.dimension_id
      WHERE d.position_id = $1
      GROUP BY d.id
      ORDER BY d.created_at ASC
    `, [id]);

    return {
      ...positionResult.rows[0],
      dimensions: dimensionsResult.rows
    };
  }

  // 创建新岗位
  static async create(data) {
    const { name, description } = data;
    const result = await db.query(
      'INSERT INTO positions (name, description) VALUES ($1, $2) RETURNING *',
      [name, description]
    );
    return result.rows[0];
  }

  // 更新岗位
  static async update(id, data) {
    const { name, description } = data;
    const result = await db.query(
      'UPDATE positions SET name = $1, description = $2 WHERE id = $3 RETURNING *',
      [name, description, id]
    );
    return result.rows[0];
  }

  // 删除岗位
  static async delete(id) {
    const result = await db.query('DELETE FROM positions WHERE id = $1 RETURNING *', [id]);
    return result.rows[0];
  }

  // 检查岗位名称是否已存在
  static async existsByName(name, excludeId = null) {
    let query = 'SELECT id FROM positions WHERE name = $1';
    let params = [name];
    
    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }
    
    const result = await db.query(query, params);
    return result.rows.length > 0;
  }

  // 获取岗位统计信息
  static async getStats() {
    const result = await db.query(`
      SELECT 
        COUNT(DISTINCT p.id) as total_positions,
        COUNT(DISTINCT d.id) as total_dimensions,
        COUNT(DISTINCT q.id) as total_questions,
        COUNT(DISTINCT ir.id) as total_interviews
      FROM positions p
      LEFT JOIN dimensions d ON p.id = d.position_id
      LEFT JOIN questions q ON d.id = q.dimension_id
      LEFT JOIN interview_records ir ON p.id = ir.position_id
    `);
    return result.rows[0];
  }
}

module.exports = Position;
