{"name": "interview-master-backend", "version": "1.0.0", "description": "Interview Master Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "db:init": "node src/database/init.js", "db:reset": "npm run migrate && npm run seed"}, "keywords": ["interview", "api", "nodejs", "express"], "author": "左浩", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "openai": "^4.20.1", "joi": "^17.11.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}