// 岗位接口
export interface Position {
  id: string
  name: string
  description?: string
  createdAt: string
  updatedAt: string
}

// 知识维度接口
export interface Dimension {
  id: string
  positionId: string
  name: string
  description?: string
  createdAt: string
  updatedAt: string
}

// 题目接口
export interface Question {
  id: string
  dimensionId: string
  title: string
  answer: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  createdAt: string
  updatedAt: string
}

// 面试记录接口
export interface InterviewRecord {
  id: string
  candidateName: string
  positionId: string
  interviewDate: string
  totalScore: number
  questions: InterviewQuestion[]
  createdAt: string
  updatedAt: string
}

// 面试题目记录
export interface InterviewQuestion {
  questionId: string
  question: Question
  candidateAnswer: string
  score: number
}

// 简历解析结果
export interface ResumeAnalysis {
  id: string
  fileName: string
  skills: string[]
  recommendedQuestions: Question[]
  createdAt: string
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页接口
export interface PaginationParams {
  page: number
  limit: number
  search?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 表单验证错误
export interface ValidationError {
  field: string
  message: string
}
