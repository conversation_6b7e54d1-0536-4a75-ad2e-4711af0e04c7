import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '首页 - 面试宝典' }
    },
    {
      path: '/questions',
      name: 'questions',
      component: () => import('@/views/QuestionManagement.vue'),
      meta: { title: '题库管理 - 面试宝典' }
    },
    {
      path: '/interview',
      name: 'interview',
      component: () => import('@/views/InterviewView.vue'),
      meta: { title: '面试进行 - 面试宝典' }
    },
    {
      path: '/records',
      name: 'records',
      component: () => import('@/views/InterviewRecords.vue'),
      meta: { title: '面试记录 - 面试宝典' }
    },
    {
      path: '/resume',
      name: 'resume',
      component: () => import('@/views/ResumeAnalysis.vue'),
      meta: { title: '简历解析 - 面试宝典' }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFound.vue'),
      meta: { title: '页面未找到 - 面试宝典' }
    }
  ]
})

// 路由守卫 - 设置页面标题
router.beforeEach((to) => {
  document.title = to.meta.title as string || '面试宝典'
})

export default router
