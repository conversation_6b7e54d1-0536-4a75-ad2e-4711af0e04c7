<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h1 class="text-9xl font-bold text-gray-300">404</h1>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">页面未找到</h2>
        <p class="mt-2 text-sm text-gray-600">
          抱歉，您访问的页面不存在或已被移动。
        </p>
        <div class="mt-6">
          <RouterLink to="/" class="btn btn-primary">
            返回首页
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
