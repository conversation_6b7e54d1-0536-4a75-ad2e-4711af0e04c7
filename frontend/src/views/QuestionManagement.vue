<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-soft">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <RouterLink to="/" class="text-2xl font-bold text-gray-900">面试宝典</RouterLink>
            <span class="ml-4 text-gray-500">/</span>
            <span class="ml-4 text-lg text-gray-700">题库管理</span>
          </div>
          <div class="flex items-center space-x-4">
            <RouterLink to="/" class="btn btn-secondary">返回首页</RouterLink>
            <button @click="showCreateModal = true" class="btn btn-primary">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              新增题目
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 筛选区域 -->
      <div class="card p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="label">岗位筛选</label>
            <select v-model="filters.positionId" class="input">
              <option value="">全部岗位</option>
              <option v-for="position in positions" :key="position.id" :value="position.id">
                {{ position.name }}
              </option>
            </select>
          </div>
          <div>
            <label class="label">难度筛选</label>
            <select v-model="filters.difficulty" class="input">
              <option value="">全部难度</option>
              <option value="easy">简单</option>
              <option value="medium">中等</option>
              <option value="hard">困难</option>
            </select>
          </div>
          <div>
            <label class="label">搜索题目</label>
            <input 
              v-model="filters.search" 
              type="text" 
              placeholder="搜索题目内容..." 
              class="input"
              @keyup.enter="loadQuestions"
            >
          </div>
          <div class="flex items-end">
            <button @click="loadQuestions" class="btn btn-primary w-full">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              搜索
            </button>
          </div>
        </div>
      </div>

      <!-- 题目列表 -->
      <div class="card">
        <div class="p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">题目列表</h2>
          <p class="text-sm text-gray-600 mt-1">
            共 {{ pagination.total }} 道题目
          </p>
        </div>

        <div v-if="loading" class="p-8 text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <p class="mt-2 text-gray-600">加载中...</p>
        </div>

        <div v-else-if="questions.length === 0" class="p-8 text-center text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="mt-2">暂无题目数据</p>
          <button @click="showCreateModal = true" class="btn btn-primary mt-4">
            创建第一道题目
          </button>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div v-for="question in questions" :key="question.id" class="p-6 hover:bg-gray-50">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getDifficultyClass(question.difficulty)">
                    {{ getDifficultyText(question.difficulty) }}
                  </span>
                  <span class="text-sm text-gray-500">{{ question.position_name }}</span>
                  <span class="text-sm text-gray-500">{{ question.dimension_name }}</span>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ question.title }}</h3>
                <div v-if="question.tags && question.tags.length > 0" class="flex flex-wrap gap-1 mb-2">
                  <span v-for="tag in question.tags" :key="tag" 
                        class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                    {{ tag }}
                  </span>
                </div>
                <p class="text-sm text-gray-500">
                  创建时间：{{ formatDate(question.created_at) }}
                </p>
              </div>
              <div class="flex items-center space-x-2 ml-4">
                <button @click="viewQuestion(question)" class="btn btn-secondary text-sm">
                  查看
                </button>
                <button @click="editQuestion(question)" class="btn btn-secondary text-sm">
                  编辑
                </button>
                <button @click="deleteQuestion(question)" class="btn btn-danger text-sm">
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              显示第 {{ (pagination.page - 1) * pagination.limit + 1 }} - 
              {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，
              共 {{ pagination.total }} 条
            </div>
            <div class="flex space-x-2">
              <button 
                @click="changePage(pagination.page - 1)"
                :disabled="pagination.page <= 1"
                class="btn btn-secondary text-sm"
                :class="{ 'opacity-50 cursor-not-allowed': pagination.page <= 1 }"
              >
                上一页
              </button>
              <button 
                @click="changePage(pagination.page + 1)"
                :disabled="pagination.page >= pagination.totalPages"
                class="btn btn-secondary text-sm"
                :class="{ 'opacity-50 cursor-not-allowed': pagination.page >= pagination.totalPages }"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 创建/编辑题目模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          {{ editingQuestion ? '编辑题目' : '新增题目' }}
        </h3>
        
        <form @submit.prevent="saveQuestion">
          <div class="space-y-4">
            <div>
              <label class="label">题目标题 *</label>
              <textarea 
                v-model="questionForm.title" 
                rows="3" 
                class="input" 
                placeholder="请输入题目内容..."
                required
              ></textarea>
            </div>
            
            <div>
              <label class="label">参考答案 *</label>
              <textarea 
                v-model="questionForm.answer" 
                rows="5" 
                class="input" 
                placeholder="请输入参考答案..."
                required
              ></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="label">难度等级 *</label>
                <select v-model="questionForm.difficulty" class="input" required>
                  <option value="easy">简单</option>
                  <option value="medium">中等</option>
                  <option value="hard">困难</option>
                </select>
              </div>
              
              <div>
                <label class="label">知识维度 *</label>
                <select v-model="questionForm.dimensionId" class="input" required>
                  <option value="">请选择知识维度</option>
                  <!-- 这里需要加载维度数据 -->
                </select>
              </div>
            </div>
            
            <div>
              <label class="label">标签</label>
              <input 
                v-model="tagsInput" 
                type="text" 
                class="input" 
                placeholder="输入标签，用逗号分隔"
                @blur="updateTags"
              >
              <div v-if="questionForm.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
                <span v-for="(tag, index) in questionForm.tags" :key="index" 
                      class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                  {{ tag }}
                  <button @click="removeTag(index)" class="ml-1 text-blue-600 hover:text-blue-800">
                    ×
                  </button>
                </span>
              </div>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary">
              {{ editingQuestion ? '更新' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { apiService } from '@/utils/api'
import type { Question, Position } from '@/types'

// 响应式数据
const loading = ref(false)
const questions = ref<Question[]>([])
const positions = ref<Position[]>([])
const showCreateModal = ref(false)
const editingQuestion = ref<Question | null>(null)

// 筛选条件
const filters = reactive({
  positionId: '',
  difficulty: '',
  search: '',
  page: 1,
  limit: 20
})

// 分页信息
const pagination = reactive({
  total: 0,
  page: 1,
  limit: 20,
  totalPages: 0
})

// 题目表单
const questionForm = reactive({
  title: '',
  answer: '',
  difficulty: 'medium' as 'easy' | 'medium' | 'hard',
  dimensionId: '',
  tags: [] as string[]
})

const tagsInput = ref('')

// 方法
const loadQuestions = async () => {
  loading.value = true
  try {
    const response = await apiService.questions.getAll(filters)
    questions.value = response.data
    Object.assign(pagination, {
      total: response.total,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages
    })
  } catch (error) {
    console.error('Failed to load questions:', error)
  } finally {
    loading.value = false
  }
}

const loadPositions = async () => {
  try {
    const response = await apiService.positions.getAll()
    positions.value = response.data
  } catch (error) {
    console.error('Failed to load positions:', error)
  }
}

const changePage = (page: number) => {
  filters.page = page
  loadQuestions()
}

const getDifficultyClass = (difficulty: string) => {
  const classes = {
    easy: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    hard: 'bg-red-100 text-red-800'
  }
  return classes[difficulty as keyof typeof classes] || classes.medium
}

const getDifficultyText = (difficulty: string) => {
  const texts = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return texts[difficulty as keyof typeof texts] || '中等'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const viewQuestion = (question: Question) => {
  // TODO: 实现查看题目详情
  console.log('View question:', question)
}

const editQuestion = (question: Question) => {
  editingQuestion.value = question
  Object.assign(questionForm, {
    title: question.title,
    answer: question.answer,
    difficulty: question.difficulty,
    dimensionId: question.dimensionId,
    tags: [...question.tags]
  })
  tagsInput.value = question.tags.join(', ')
  showCreateModal.value = true
}

const deleteQuestion = async (question: Question) => {
  if (!confirm(`确定要删除题目"${question.title}"吗？`)) return
  
  try {
    await apiService.questions.delete(question.id)
    await loadQuestions()
  } catch (error) {
    console.error('Failed to delete question:', error)
    alert('删除失败，请重试')
  }
}

const saveQuestion = async () => {
  try {
    if (editingQuestion.value) {
      await apiService.questions.update(editingQuestion.value.id, questionForm)
    } else {
      await apiService.questions.create(questionForm)
    }
    
    closeModal()
    await loadQuestions()
  } catch (error) {
    console.error('Failed to save question:', error)
    alert('保存失败，请重试')
  }
}

const closeModal = () => {
  showCreateModal.value = false
  editingQuestion.value = null
  Object.assign(questionForm, {
    title: '',
    answer: '',
    difficulty: 'medium',
    dimensionId: '',
    tags: []
  })
  tagsInput.value = ''
}

const updateTags = () => {
  if (tagsInput.value.trim()) {
    questionForm.tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag)
  }
}

const removeTag = (index: number) => {
  questionForm.tags.splice(index, 1)
  tagsInput.value = questionForm.tags.join(', ')
}

// 生命周期
onMounted(() => {
  loadQuestions()
  loadPositions()
})
</script>
