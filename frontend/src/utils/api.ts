import axios from 'axios'
import type { ApiResponse, PaginatedResponse } from '@/types'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，可以跳转到登录页
          console.error('Unauthorized access')
          break
        case 403:
          console.error('Forbidden access')
          break
        case 404:
          console.error('Resource not found')
          break
        case 500:
          console.error('Server error')
          break
        default:
          console.error('Unknown error:', data?.error || error.message)
      }
      
      return Promise.reject(data || { error: 'Request failed' })
    } else if (error.request) {
      // 网络错误
      console.error('Network error')
      return Promise.reject({ error: 'Network error' })
    } else {
      // 其他错误
      console.error('Request setup error:', error.message)
      return Promise.reject({ error: error.message })
    }
  }
)

export default api

// API方法封装
export const apiService = {
  // 岗位相关API
  positions: {
    getAll: (): Promise<ApiResponse> => api.get('/positions'),
    getById: (id: string, includeDimensions = false): Promise<ApiResponse> => 
      api.get(`/positions/${id}${includeDimensions ? '?include=dimensions' : ''}`),
    create: (data: { name: string; description?: string }): Promise<ApiResponse> => 
      api.post('/positions', data),
    update: (id: string, data: { name: string; description?: string }): Promise<ApiResponse> => 
      api.put(`/positions/${id}`, data),
    delete: (id: string): Promise<ApiResponse> => api.delete(`/positions/${id}`),
    getStats: (): Promise<ApiResponse> => api.get('/positions/stats')
  },

  // 题目相关API
  questions: {
    getAll: (params?: {
      page?: number
      limit?: number
      positionId?: string
      dimensionId?: string
      difficulty?: string
      search?: string
    }): Promise<PaginatedResponse<any>> => api.get('/questions', { params }),
    
    getById: (id: string): Promise<ApiResponse> => api.get(`/questions/${id}`),
    
    getRandom: (params?: {
      positionId?: string
      dimensionIds?: string[]
      difficulty?: string
      count?: number
    }): Promise<ApiResponse> => api.get('/questions/random', { params }),
    
    getRecommended: (skills: string[], limit = 20): Promise<ApiResponse> => 
      api.get('/questions/recommend', { 
        params: { skills: skills.join(','), limit } 
      }),
    
    create: (data: {
      dimensionId: string
      title: string
      answer: string
      difficulty?: 'easy' | 'medium' | 'hard'
      tags?: string[]
    }): Promise<ApiResponse> => api.post('/questions', data),
    
    update: (id: string, data: {
      dimensionId: string
      title: string
      answer: string
      difficulty?: 'easy' | 'medium' | 'hard'
      tags?: string[]
    }): Promise<ApiResponse> => api.put(`/questions/${id}`, data),
    
    delete: (id: string): Promise<ApiResponse> => api.delete(`/questions/${id}`)
  },

  // 面试记录相关API
  interviews: {
    getAll: (): Promise<ApiResponse> => api.get('/interviews'),
    getById: (id: string): Promise<ApiResponse> => api.get(`/interviews/${id}`),
    create: (data: any): Promise<ApiResponse> => api.post('/interviews', data),
    update: (id: string, data: any): Promise<ApiResponse> => api.put(`/interviews/${id}`, data),
    delete: (id: string): Promise<ApiResponse> => api.delete(`/interviews/${id}`)
  },

  // 简历解析相关API
  resume: {
    parse: (file: File): Promise<ApiResponse> => {
      const formData = new FormData()
      formData.append('resume', file)
      return api.post('/resume/parse', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    }
  },

  // 认证相关API
  auth: {
    login: (credentials: { username: string; password: string }): Promise<ApiResponse> => 
      api.post('/auth/login', credentials),
    register: (userData: { username: string; email: string; password: string }): Promise<ApiResponse> => 
      api.post('/auth/register', userData),
    logout: (): Promise<ApiResponse> => api.post('/auth/logout'),
    getProfile: (): Promise<ApiResponse> => api.get('/auth/profile')
  }
}
