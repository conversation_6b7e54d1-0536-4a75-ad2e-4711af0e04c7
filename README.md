# 面试宝典 (Interview Master)

一个专业的岗位维度化面试题库管理与调用平台，支持随机抽题、题目解析、面试记录与评分，并可根据候选人简历自动筛选题目。

## 🚀 功能特性

- **题库管理**: 按岗位→知识维度→题目的三级结构管理
- **面试管理**: 随机抽题、实时记录、智能评分
- **简历解析**: 自动提取技能关键词，推荐相关题目
- **记录管理**: 完整的面试记录保存与导出功能
- **用户友好**: 现代化UI设计，操作简单直观

## 🛠️ 技术栈

### 前端
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI框架**: Tailwind CSS + Headless UI
- **图标**: Heroicons

### 后端
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: PostgreSQL
- **认证**: JWT
- **文件处理**: Multer
- **简历解析**: OpenAI API / PDF-Parse + Mammoth

## 📁 项目结构

```
InterviewMaster/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── database/       # 数据库配置
│   │   ├── services/       # 业务服务
│   │   └── utils/          # 工具函数
│   └── package.json
├── frontend/               # 前端Vue应用
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── stores/         # 状态管理
│   │   ├── types/          # TypeScript类型
│   │   └── utils/          # 工具函数
│   └── package.json
├── docs/                   # 文档
├── *.html                  # 现有的静态面试记录文件
└── prd.md                  # 产品需求文档
```

## 🚦 快速开始

### 环境要求
- Node.js >= 18.0.0
- PostgreSQL >= 13.0
- npm 或 yarn

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 环境配置

1. 复制后端环境配置文件：
```bash
cd backend
cp .env.example .env
```

2. 编辑 `.env` 文件，配置数据库连接和其他必要参数

### 数据库初始化

```bash
cd backend
npm run migrate  # 创建数据库表
npm run seed     # 插入初始数据
```

### 启动服务

```bash
# 启动后端服务 (端口: 3000)
cd backend
npm run dev

# 启动前端服务 (端口: 5173)
cd frontend
npm run dev
```

访问 http://localhost:5173 查看应用

## 📊 API文档

### 主要接口

- `GET /api/positions` - 获取岗位列表
- `GET /api/questions` - 获取题目列表
- `POST /api/interviews` - 创建面试记录
- `POST /api/resume/parse` - 解析简历

详细API文档请参考 `/docs/api.md`

## 🎯 开发计划

- [x] 系统架构设计与环境搭建
- [ ] 数据库设计与初始化
- [ ] 后端API开发
- [ ] 前端页面开发
- [ ] 简历解析功能集成
- [ ] 系统集成与测试

## 📝 许可证

MIT License

## 👨‍💻 作者

左浩 - 项目负责人

---

**注意**: 现有的HTML面试记录文件 (`*.html`) 将保持不变，新系统将与之并存。
